/**
 * Google Apps Script for El-cut Dual Account Coordination
 *
 * This script manages communication between main and secondary extensions
 * for intelligent account rotation to avoid Eldorado price change limits.
 *
 * Sheet Structure (Elcut tab):
 * - realm: string (e.g., "Firemaw:Alliance")
 * - account: string ("main" or "secondary")
 * - target_price: number (USD price to set)
 * - action: string ("undercut", "hold", "limited", "completed")
 * - timestamp: number (when instruction was created)
 * - change_count: number (consecutive changes for this account)
 * - last_change: number (timestamp of last price change)
 */

const ELCUT_SPREADSHEET_ID = '1b1PgY50wa7Kg1k_JOTFiLW6e6jSwAbQ4N323A0oGGps';
const ELCUT_SHEET_NAME = 'Elcut';

// Configuration
const MAX_CHANGES_BEFORE_ROTATION = 3;  // Switch accounts after 3 changes
const ROTATION_COOLDOWN_MS = 2 * 60 * 1000;  // 2 minutes between account switches
const INSTRUCTION_TIMEOUT_MS = 5 * 60 * 1000;  // 5 minutes before instruction expires

function doPost(e) {
  let body;
  try {
    body = JSON.parse(e.postData.contents);
  } catch {
    return createErrorResponse('Invalid JSON');
  }

  const { action, realm, account, target_price, change_count } = body;
  
  if (!action || !realm) {
    return createErrorResponse('Missing required fields: action, realm');
  }

  try {
    const ss = SpreadsheetApp.openById(ELCUT_SPREADSHEET_ID);
    let sheet = ss.getSheetByName(ELCUT_SHEET_NAME);

    // Create sheet if it doesn't exist
    if (!sheet) {
      sheet = ss.insertSheet(ELCUT_SHEET_NAME);
      // Add headers
      sheet.getRange(1, 1, 1, 7).setValues([
        ['realm', 'account', 'target_price', 'action', 'timestamp', 'change_count', 'last_change']
      ]);
    }

    const data = sheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indices
    const colIndex = {
      realm: headers.indexOf('realm'),
      account: headers.indexOf('account'),
      target_price: headers.indexOf('target_price'),
      action: headers.indexOf('action'),
      timestamp: headers.indexOf('timestamp'),
      change_count: headers.indexOf('change_count'),
      last_change: headers.indexOf('last_change')
    };

    const now = Date.now();

    if (action === 'set_instruction') {
      // Main extension setting instruction for secondary
      setInstruction(sheet, colIndex, realm, account, target_price, change_count, now);
      return createSuccessResponse({ message: 'Instruction set', realm, account });
      
    } else if (action === 'get_instructions') {
      // Secondary extension getting its instructions
      const instructions = getInstructions(sheet, colIndex, account, now);
      return createSuccessResponse({ instructions });
      
    } else if (action === 'report_completion') {
      // Secondary extension reporting completion
      reportCompletion(sheet, colIndex, realm, now);
      return createSuccessResponse({ message: 'Completion reported', realm });
      
    } else if (action === 'check_rotation') {
      // Main extension checking if it should rotate accounts
      const shouldRotate = checkShouldRotate(sheet, colIndex, account, now);
      return createSuccessResponse({ should_rotate: shouldRotate, current_account: account });
      
    } else if (action === 'mark_limited') {
      // Mark account as limited for a realm
      markAccountLimited(sheet, colIndex, realm, account, now);
      return createSuccessResponse({ message: 'Account marked as limited', realm, account });
    }

    return createErrorResponse('Unknown action');

  } catch (error) {
    console.error('Error in doPost:', error);
    return createErrorResponse('Internal server error');
  }
}

function doGet() {
  try {
    const ss = SpreadsheetApp.openById(ELCUT_SPREADSHEET_ID);
    const sheet = ss.getSheetByName(ELCUT_SHEET_NAME);
    
    if (!sheet) {
      return createSuccessResponse({ data: [] });
    }

    const data = sheet.getDataRange().getValues();
    const [headers, ...rows] = data;
    
    const result = rows.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });

    return createSuccessResponse({ data: result });

  } catch (error) {
    console.error('Error in doGet:', error);
    return createErrorResponse('Internal server error');
  }
}

function setInstruction(sheet, colIndex, realm, account, targetPrice, changeCount, timestamp) {
  const data = sheet.getDataRange().getValues();
  const rows = data.slice(1);
  
  // Find existing row for this realm
  let rowIndex = -1;
  for (let i = 0; i < rows.length; i++) {
    if (rows[i][colIndex.realm] === realm) {
      rowIndex = i + 2; // +2 because of header and 0-based index
      break;
    }
  }

  const values = [realm, account, targetPrice, 'undercut', timestamp, changeCount || 0, timestamp];

  if (rowIndex > -1) {
    // Update existing row
    sheet.getRange(rowIndex, 1, 1, values.length).setValues([values]);
  } else {
    // Add new row
    sheet.appendRow(values);
  }
}

function getInstructions(sheet, colIndex, account, currentTime) {
  const data = sheet.getDataRange().getValues();
  const rows = data.slice(1);
  
  const instructions = [];
  
  for (const row of rows) {
    const rowAccount = row[colIndex.account];
    const action = row[colIndex.action];
    const timestamp = row[colIndex.timestamp];
    
    // Only return instructions for this account that are recent and actionable
    if (rowAccount === account && 
        action === 'undercut' && 
        (currentTime - timestamp) < INSTRUCTION_TIMEOUT_MS) {
      
      instructions.push({
        realm: row[colIndex.realm],
        target_price: row[colIndex.target_price],
        action: action,
        timestamp: timestamp
      });
    }
  }
  
  return instructions;
}

function reportCompletion(sheet, colIndex, realm, timestamp) {
  const data = sheet.getDataRange().getValues();
  const rows = data.slice(1);
  
  // Find the row for this realm and mark as completed
  for (let i = 0; i < rows.length; i++) {
    if (rows[i][colIndex.realm] === realm) {
      const rowIndex = i + 2;
      sheet.getRange(rowIndex, colIndex.action + 1).setValue('completed');
      sheet.getRange(rowIndex, colIndex.last_change + 1).setValue(timestamp);
      break;
    }
  }
}

function checkShouldRotate(sheet, colIndex, currentAccount, currentTime) {
  const data = sheet.getDataRange().getValues();
  const rows = data.slice(1);
  
  let recentChanges = 0;
  let lastChangeTime = 0;
  
  // Count recent changes for current account
  for (const row of rows) {
    if (row[colIndex.account] === currentAccount) {
      const changeCount = row[colIndex.change_count] || 0;
      const lastChange = row[colIndex.last_change] || 0;
      
      recentChanges = Math.max(recentChanges, changeCount);
      lastChangeTime = Math.max(lastChangeTime, lastChange);
    }
  }
  
  // Rotate if we've hit the change limit and enough time has passed
  const timeSinceLastChange = currentTime - lastChangeTime;
  return recentChanges >= MAX_CHANGES_BEFORE_ROTATION && 
         timeSinceLastChange > ROTATION_COOLDOWN_MS;
}

function markAccountLimited(sheet, colIndex, realm, account, timestamp) {
  const data = sheet.getDataRange().getValues();
  const rows = data.slice(1);
  
  // Find or create row for this realm
  let rowIndex = -1;
  for (let i = 0; i < rows.length; i++) {
    if (rows[i][colIndex.realm] === realm) {
      rowIndex = i + 2;
      break;
    }
  }

  if (rowIndex > -1) {
    sheet.getRange(rowIndex, colIndex.action + 1).setValue('limited');
    sheet.getRange(rowIndex, colIndex.timestamp + 1).setValue(timestamp);
  } else {
    sheet.appendRow([realm, account, 0, 'limited', timestamp, 0, timestamp]);
  }
}

function createSuccessResponse(data) {
  return ContentService
    .createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
}

function createErrorResponse(message) {
  return ContentService
    .createTextOutput(JSON.stringify({ error: message }))
    .setMimeType(ContentService.MimeType.JSON);
}
