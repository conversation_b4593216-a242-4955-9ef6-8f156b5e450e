# El-cut Dual Account Setup Instructions

## Overview

This system implements intelligent dual-account coordination to avoid Eldorado's price change limits. Instead of spamming until limited, it proactively rotates between accounts and uses the secondary account when the main account gets rate limited.

## Strategy

- **Conservative Rotation**: Switch accounts every 3 price changes
- **Smart Fallback**: If main account gets limited, immediately delegate to secondary
- **Timing**: 2-3 minute cooldowns between account switches
- **Communication**: Google Sheets + Apps Script as middleware

## Setup Steps

### 1. Create New Google Apps Script

1. Go to your existing Google Sheet: https://docs.google.com/spreadsheets/d/1b1PgY50wa7Kg1k_JOTFiLW6e6jSwAbQ4N323A0oGGps/edit
2. Create a new tab called "Elcut" (you mentioned you already did this)
3. Go to Extensions → Apps Script
4. Create a new script file called "elcut-coordination.gs"
5. Copy the content from `elcut-appscript.js` into this new file
6. Save and deploy as a web app:
   - Click "Deploy" → "New deployment"
   - Type: Web app
   - Execute as: Me
   - Who has access: Anyone
   - Copy the deployment URL

### 2. Update Script URLs

1. In your main extension's `content.js`, replace `YOUR_NEW_SCRIPT_ID_HERE` with your actual script deployment URL
2. In the secondary extension's `content.js` and `manifest.json`, replace `YOUR_SCRIPT_ID_HERE` with the same URL

### 3. Install Secondary Extension

1. Create a new folder for the secondary extension
2. Copy all files from the `secondary-extension/` folder
3. Update the script URL in `manifest.json` and `content.js`
4. Load the extension in Chrome:
   - Go to chrome://extensions/
   - Enable Developer mode
   - Click "Load unpacked"
   - Select the secondary extension folder

### 4. Setup Secondary Account

1. Open a new Chrome profile or incognito window
2. Log into your secondary Eldorado account
3. Navigate to Eldorado.gg
4. The secondary extension should show a small status indicator in the top-right
5. Click "Start Monitoring" to activate it

### 5. Configure Main Extension

1. In your main extension, you'll now see a "Dual Account: OFF" button in the title bar
2. Click it to enable dual account mode
3. The button will turn green and show "Dual Account: ON"

## How It Works

### Main Extension Logic

1. **Before each price change**: Checks if it should rotate to secondary account
2. **On rate limiting**: Immediately delegates to secondary account
3. **Change tracking**: Counts consecutive changes per account
4. **Smart rotation**: Switches accounts every 3 changes or when limited

### Secondary Extension Logic

1. **Polling**: Checks Google Sheet every 8 seconds for instructions
2. **Execution**: Only changes prices when instructed by main extension
3. **Reporting**: Reports completion back to coordination system
4. **Minimal UI**: Simple start/stop toggle and status display

### Coordination Sheet Structure

The "Elcut" tab will have these columns:
- `realm`: Realm identifier (e.g., "Firemaw:Alliance")
- `account`: Which account should act ("main" or "secondary")
- `target_price`: USD price to set
- `action`: Current action ("undercut", "hold", "limited", "completed")
- `timestamp`: When instruction was created
- `change_count`: Consecutive changes for this account
- `last_change`: Timestamp of last price change

## Benefits

1. **No More Spam**: Proactive rotation prevents hitting limits
2. **Faster Recovery**: Immediate fallback when limited
3. **Always Competitive**: One account always available for undercutting
4. **Smart Timing**: Respects Eldorado's rate limits while maximizing coverage
5. **Minimal Disruption**: Works with your existing workflow

## Monitoring

- Main extension shows "Dual Account: ON/OFF" status
- Secondary extension shows monitoring status in top-right corner
- Enhanced logging shows delegation and coordination activities
- Google Sheet shows real-time coordination state

## Troubleshooting

### Secondary Extension Not Responding
- Check if it's logged into the correct account
- Verify the script URL is correct
- Check browser console for errors

### Coordination Not Working
- Verify Google Apps Script is deployed correctly
- Check that both extensions have the same script URL
- Ensure the "Elcut" tab exists in your Google Sheet

### Rate Limiting Still Occurring
- The system prevents most rate limiting but can't eliminate it entirely
- If both accounts get limited, wait 5 minutes before resuming
- Consider increasing delays between price changes

## Advanced Configuration

You can modify these constants in the Apps Script:
- `MAX_CHANGES_BEFORE_ROTATION`: Default 3 changes
- `ROTATION_COOLDOWN_MS`: Default 2 minutes
- `INSTRUCTION_TIMEOUT_MS`: Default 5 minutes

## Security Notes

- The coordination happens through your own Google Sheet
- No external services involved beyond Google
- Both extensions only communicate through the sheet
- All price changes still go through Eldorado's official API

## Next Steps

1. Test with a single realm first
2. Monitor the coordination in the Google Sheet
3. Gradually enable more realms
4. Adjust timing if needed based on your experience

The system is designed to be conservative and respect Eldorado's limits while maximizing your competitive advantage.
