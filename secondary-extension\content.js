/**
 * El-cut Secondary Extension - Content Script
 * 
 * This is a lightweight extension that only executes price changes
 * when instructed by the main extension via Google Sheets.
 */

(() => {
  const API_BASE = location.origin;
  const SHEET_URL = 'https://script.google.com/macros/s/AKfycbxZwZvLLHWgeHAkfZJUwzZwJu4CSBht2xedrmvRo_sONQYoR-t8DrOQld42jhOmMVQ4vA/exec';
  const ACCOUNT_TYPE = 'secondary';
  const EXPECTED_ACCOUNT_NAME = 'Bonyadi'; // Secondary account name
  const POLL_INTERVAL = 8000; // 8 seconds
  const UND_STEP = 0.00001;

  let isActive = false;
  let pollInterval = null;
  let statusElement = null;

  // Initialize when page loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init() {
    createStatusUI();
    loadSettings();
    verifyAccount();
  }

  async function verifyAccount() {
    try {
      // Check if we're logged into the correct account
      const response = await fetch(`${API_BASE}/api/predefinedOffersUser/me/?pageSize=1`);
      if (response.ok) {
        const data = await response.json();
        if (data.results && data.results.length > 0) {
          const userInfo = data.results[0].user;
          const currentAccountName = userInfo.username || userInfo.name || 'Unknown';

          if (currentAccountName !== EXPECTED_ACCOUNT_NAME) {
            updateStatus(`⚠️ Wrong account! Expected: ${EXPECTED_ACCOUNT_NAME}, Current: ${currentAccountName}`, 'red');
            console.warn(`Account mismatch: Expected ${EXPECTED_ACCOUNT_NAME}, got ${currentAccountName}`);
          } else {
            updateStatus(`✓ Correct account: ${currentAccountName}`, 'green');
            console.log(`Account verified: ${currentAccountName}`);
          }
        }
      }
    } catch (error) {
      console.warn('Could not verify account:', error);
      updateStatus('Account verification failed', 'orange');
    }
  }

  function createStatusUI() {
    // Create a small status indicator
    statusElement = document.createElement('div');
    statusElement.id = 'elcut-secondary-status';
    statusElement.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #f0f0f0;
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 8px 12px;
      font-family: Arial, sans-serif;
      font-size: 12px;
      z-index: 10000;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;
    
    statusElement.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <span id="status-text">El-cut Secondary: Inactive</span>
        <button id="toggle-btn" style="padding: 2px 6px; font-size: 11px;">Start</button>
      </div>
    `;
    
    document.body.appendChild(statusElement);
    
    // Add click handler for toggle button
    document.getElementById('toggle-btn').addEventListener('click', toggleActive);
  }

  function toggleActive() {
    isActive = !isActive;
    
    if (isActive) {
      startPolling();
      updateStatus('Active - Waiting for instructions', 'green');
      document.getElementById('toggle-btn').textContent = 'Stop';
    } else {
      stopPolling();
      updateStatus('Inactive', 'gray');
      document.getElementById('toggle-btn').textContent = 'Start';
    }
    
    // Save state
    chrome.storage.local.set({ elcutSecondaryActive: isActive });
  }

  function loadSettings() {
    chrome.storage.local.get(['elcutSecondaryActive'], (result) => {
      if (result.elcutSecondaryActive) {
        isActive = true;
        startPolling();
        updateStatus('Active - Waiting for instructions', 'green');
        document.getElementById('toggle-btn').textContent = 'Stop';
      }
    });
  }

  function updateStatus(text, color = 'black') {
    const statusText = document.getElementById('status-text');
    if (statusText) {
      statusText.textContent = `El-cut Secondary: ${text}`;
      statusText.style.color = color;
    }
  }

  function startPolling() {
    if (pollInterval) return;
    
    pollInterval = setInterval(async () => {
      if (!isActive) return;
      
      try {
        await checkForInstructions();
      } catch (error) {
        console.error('Error checking instructions:', error);
        updateStatus('Error checking instructions', 'red');
      }
    }, POLL_INTERVAL);
  }

  function stopPolling() {
    if (pollInterval) {
      clearInterval(pollInterval);
      pollInterval = null;
    }
  }

  async function checkForInstructions() {
    try {
      console.log('Checking for instructions...');

      const response = await fetch(SHEET_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          action: 'get_instructions',
          account: ACCOUNT_TYPE
        })
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Instructions result:', result);

      if (result.error) {
        console.error('Sheet error:', result.error);
        updateStatus(`Sheet error: ${result.error}`, 'red');
        return;
      }

      const instructions = result.instructions || [];

      if (instructions.length === 0) {
        updateStatus('Active - No instructions', 'green');
        return;
      }

      updateStatus(`Processing ${instructions.length} instruction(s)`, 'blue');

      // Process each instruction
      for (const instruction of instructions) {
        await processInstruction(instruction);

        // Add delay between instructions
        if (instructions.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }

    } catch (error) {
      console.error('Error in checkForInstructions:', error);
      updateStatus(`Connection error: ${error.message}`, 'red');
    }
  }

  async function processInstruction(instruction) {
    const { realm, target_price, action } = instruction;
    
    if (action !== 'undercut') {
      return;
    }

    updateStatus(`Undercutting ${realm}...`, 'orange');
    
    try {
      const success = await setPriceForRealm(realm, target_price);
      
      if (success) {
        updateStatus(`✓ ${realm} updated`, 'green');
        
        // Report completion back to sheet
        await fetch(SHEET_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'report_completion',
            realm: realm,
            timestamp: Date.now()
          })
        });
        
      } else {
        updateStatus(`✗ Failed: ${realm}`, 'red');
      }
      
    } catch (error) {
      console.error(`Error processing ${realm}:`, error);
      updateStatus(`✗ Error: ${realm}`, 'red');
    }
  }

  async function setPriceForRealm(realmKey, usdPrice) {
    try {
      // Get offers for this realm
      const offerResponse = await fetch(`${API_BASE}/api/predefinedOffersUser/me/?pageSize=100`);
      
      if (!offerResponse.ok) {
        console.error(`Failed to fetch offers: ${offerResponse.status}`);
        return false;
      }
      
      const offerData = await offerResponse.json();
      const offers = offerData.results || [];
      
      // Filter offers for the specific realm
      const realmOffers = offers.filter(offer => {
        const offerRealmKey = `${offer.tradeEnvironment.realm}:${offer.tradeEnvironment.faction}`;
        return offerRealmKey === realmKey;
      });
      
      if (realmOffers.length === 0) {
        console.warn(`No offers found for ${realmKey}`);
        return false;
      }
      
      // Get XSRF token
      const cookie = document.cookie.split('; ').find(c => c.startsWith('__Host-XSRF-TOKEN='));
      const token = cookie && decodeURIComponent(cookie.split('=')[1]);
      
      if (!token) {
        console.error('XSRF token not found');
        return false;
      }
      
      // Update all offers for this realm
      const updatePromises = realmOffers.map(offer =>
        fetch(`${API_BASE}/api/predefinedOffersUser/me/${offer.id}/changePrice/`, {
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'X-XSRF-TOKEN': token
          },
          body: JSON.stringify({
            amount: usdPrice,
            currency: 'USD'
          })
        })
      );
      
      const results = await Promise.all(updatePromises);
      const allSuccessful = results.every(r => r.ok);
      
      if (allSuccessful) {
        console.log(`Successfully updated ${realmOffers.length} offers for ${realmKey} to ${usdPrice}`);
        return true;
      } else {
        console.error(`Some updates failed for ${realmKey}`);
        return false;
      }
      
    } catch (error) {
      console.error(`Error setting price for ${realmKey}:`, error);
      return false;
    }
  }

})();
