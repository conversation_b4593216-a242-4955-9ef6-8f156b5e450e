document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const toggleBtn = document.getElementById('toggleBtn');
  
  // Load current state
  chrome.storage.local.get(['elcutSecondaryActive'], function(result) {
    const isActive = result.elcutSecondaryActive || false;
    updateUI(isActive);
  });
  
  // Toggle button click handler
  toggleBtn.addEventListener('click', function() {
    chrome.storage.local.get(['elcutSecondaryActive'], function(result) {
      const currentState = result.elcutSecondaryActive || false;
      const newState = !currentState;
      
      chrome.storage.local.set({ elcutSecondaryActive: newState }, function() {
        updateUI(newState);
        
        // Send message to content script to update state
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          if (tabs[0] && tabs[0].url.includes('eldorado.gg')) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'toggleActive',
              active: newState
            });
          }
        });
      });
    });
  });
  
  function updateUI(isActive) {
    if (isActive) {
      statusDiv.textContent = 'Status: Active - Monitoring for instructions';
      statusDiv.className = 'status active';
      toggleBtn.textContent = 'Stop Monitoring';
      toggleBtn.className = 'btn-secondary';
    } else {
      statusDiv.textContent = 'Status: Inactive';
      statusDiv.className = 'status inactive';
      toggleBtn.textContent = 'Start Monitoring';
      toggleBtn.className = 'btn-primary';
    }
  }
});
