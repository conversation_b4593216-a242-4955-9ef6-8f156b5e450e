<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: Arial, sans-serif;
      font-size: 14px;
    }
    .header {
      text-align: center;
      margin-bottom: 15px;
      color: #333;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
      text-align: center;
    }
    .status.active {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.inactive {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .info {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      color: #666;
    }
    button {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }
    .btn-primary {
      background: #007bff;
      color: white;
    }
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>El-cut Secondary</h3>
  </div>
  
  <div id="status" class="status inactive">
    Status: Inactive
  </div>
  
  <button id="toggleBtn" class="btn-primary">Start Monitoring</button>
  
  <div class="info">
    <strong>Instructions:</strong><br>
    1. Make sure you're logged into your secondary Eldorado account<br>
    2. Click "Start Monitoring" to begin listening for price change instructions<br>
    3. The extension will automatically execute price changes when instructed by the main extension
  </div>

  <script src="popup.js"></script>
</body>
</html>
