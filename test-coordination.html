<!DOCTYPE html>
<html>
<head>
    <title>El-cut Coordination Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        #log { background: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>El-cut Dual Account Coordination Test</h1>
    
    <div class="test-section">
        <h3>Configuration</h3>
        <label>Script URL: </label>
        <input type="text" id="scriptUrl" placeholder="https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec" style="width: 500px;">
        <br><br>
        <button onclick="testConnection()">Test Connection</button>
    </div>

    <div class="test-section">
        <h3>Main Account Actions</h3>
        <button onclick="setInstruction()">Set Instruction for Secondary</button>
        <button onclick="checkRotation()">Check Should Rotate</button>
        <button onclick="markLimited()">Mark Account Limited</button>
    </div>

    <div class="test-section">
        <h3>Secondary Account Actions</h3>
        <button onclick="getInstructions()">Get Instructions</button>
        <button onclick="reportCompletion()">Report Completion</button>
    </div>

    <div class="test-section">
        <h3>Data View</h3>
        <button onclick="viewAllData()">View All Coordination Data</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h3>Log</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = type;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getScriptUrl() {
            const url = document.getElementById('scriptUrl').value.trim();
            if (!url) {
                log('Please enter the script URL first', 'error');
                return null;
            }
            return url;
        }

        async function testConnection() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Testing connection...', 'info');
                const response = await fetch(url);
                const data = await response.json();
                log('Connection successful!', 'success');
                log(`Response: ${JSON.stringify(data)}`, 'info');
            } catch (error) {
                log(`Connection failed: ${error.message}`, 'error');
            }
        }

        async function setInstruction() {
            const url = getScriptUrl();
            if (!url) return;

            const testData = {
                action: 'set_instruction',
                realm: 'Firemaw:Alliance',
                account: 'secondary',
                target_price: 0.12345,
                change_count: 1
            };

            try {
                log('Setting instruction for secondary account...', 'info');
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const result = await response.json();
                log(`Set instruction result: ${JSON.stringify(result)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error setting instruction: ${error.message}`, 'error');
            }
        }

        async function getInstructions() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Getting instructions for secondary account...', 'info');
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_instructions',
                        account: 'secondary'
                    })
                });
                const result = await response.json();
                log(`Instructions: ${JSON.stringify(result)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error getting instructions: ${error.message}`, 'error');
            }
        }

        async function reportCompletion() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Reporting completion...', 'info');
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'report_completion',
                        realm: 'Firemaw:Alliance'
                    })
                });
                const result = await response.json();
                log(`Completion report: ${JSON.stringify(result)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error reporting completion: ${error.message}`, 'error');
            }
        }

        async function checkRotation() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Checking if should rotate accounts...', 'info');
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'check_rotation',
                        account: 'main'
                    })
                });
                const result = await response.json();
                log(`Rotation check: ${JSON.stringify(result)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error checking rotation: ${error.message}`, 'error');
            }
        }

        async function markLimited() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Marking account as limited...', 'info');
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'mark_limited',
                        realm: 'Firemaw:Alliance',
                        account: 'main'
                    })
                });
                const result = await response.json();
                log(`Mark limited result: ${JSON.stringify(result)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error marking limited: ${error.message}`, 'error');
            }
        }

        async function viewAllData() {
            const url = getScriptUrl();
            if (!url) return;

            try {
                log('Fetching all coordination data...', 'info');
                const response = await fetch(url);
                const result = await response.json();
                log(`All data: ${JSON.stringify(result, null, 2)}`, result.error ? 'error' : 'success');
            } catch (error) {
                log(`Error fetching data: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Auto-save script URL
        document.getElementById('scriptUrl').addEventListener('input', function() {
            localStorage.setItem('elcut_test_script_url', this.value);
        });

        // Load saved script URL
        window.addEventListener('load', function() {
            const saved = localStorage.getItem('elcut_test_script_url');
            if (saved) {
                document.getElementById('scriptUrl').value = saved;
            }
        });
    </script>
</body>
</html>
